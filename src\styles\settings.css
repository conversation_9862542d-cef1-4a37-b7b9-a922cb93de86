/* Settings Panel Styles */

/* Settings Panel Overlay */
.settings-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 1000;
  pointer-events: none;
  transition: all var(--transition-normal) ease-in-out;
}

.settings-panel:not(.hidden) {
  background: var(--bg-overlay);
  backdrop-filter: blur(4px);
  pointer-events: all;
}

.settings-panel.hidden {
  pointer-events: none;
}

/* Settings Content Container */
.settings-content {
  background: var(--bg-primary);
  box-shadow: var(--shadow-xl);
  width: 400px;
  height: 100vh;
  overflow-y: auto;
  margin-left: auto;
  transform: translateX(100%);
  transition: transform var(--transition-normal) ease-in-out;
  pointer-events: all;
}

.settings-panel:not(.hidden) .settings-content {
  transform: translateX(0);
}

/* Settings Header */
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-xl) var(--spacing-lg);
  border-bottom: 2px solid var(--border-color);
  position: sticky;
  top: 0;
  background: var(--bg-primary);
  z-index: 10;
}

.settings-header h2 {
  margin: 0;
  font-size: var(--font-size-2xl);
  font-weight: 700;
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.close-btn {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--text-secondary);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--transition-fast);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: scale(1.1);
}

.close-btn:active {
  transform: scale(0.95);
}

/* Settings Sections */
.settings-sections {
  padding: var(--spacing-lg) var(--spacing-xl) var(--spacing-xl);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xl);
}

.settings-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.settings-section h3 {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

/* Setting Items */
.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.setting-item.horizontal {
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}

.setting-item label {
  font-weight: 500;
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.setting-item input[type="number"],
.setting-item input[type="color"],
.setting-item input[type="file"],
.setting-item select {
  padding: var(--spacing-sm) var(--spacing-md);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  background: var(--bg-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.setting-item input[type="number"]:focus,
.setting-item input[type="color"]:focus,
.setting-item input[type="file"]:focus,
.setting-item select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-item input[type="checkbox"] {
  width: 20px;
  height: 20px;
  accent-color: var(--primary-color);
  cursor: pointer;
}

/* Volume Control */
.volume-control {
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-md);
}

.volume-control label {
  min-width: 60px;
}

.volume-control input[type="range"] {
  flex: 1;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.volume-control input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.volume-control input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.2);
}

.volume-control input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  transition: all var(--transition-fast);
}

#volume-display {
  min-width: 40px;
  font-weight: 600;
  color: var(--primary-color);
  font-size: var(--font-size-sm);
}

/* Track Selection */
.track-selection select {
  flex: 1;
}

/* Control Button Styles for Settings */
.control-btn.secondary.small {
  padding: var(--spacing-sm) var(--spacing-md);
  font-size: var(--font-size-xs);
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.control-btn.secondary.small:hover {
  background: var(--primary-color);
  color: var(--text-white);
  border-color: var(--primary-color);
}

.control-btn.secondary.small:active {
  transform: scale(0.95);
}

/* Save Button */
.save-btn {
  background: var(--primary-color);
  color: var(--text-white);
  border: none;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
  width: 100%;
  margin-top: var(--spacing-sm);
}

.save-btn:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.save-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Changed Settings Highlight */
.setting-item input.changed,
.setting-item select.changed {
  border-color: var(--success-color);
  box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
  animation: highlightChange 0.5s ease-in-out;
}

/* Animations */
@keyframes slideInRight {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes highlightChange {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
}

/* Responsive Design for Settings */
@media (max-width: 768px) {
  .settings-content {
    width: 350px;
  }

  .settings-header {
    padding: var(--spacing-lg) var(--spacing-lg) var(--spacing-md);
  }

  .settings-sections {
    padding: var(--spacing-md) var(--spacing-lg) var(--spacing-lg);
    gap: var(--spacing-lg);
  }

  .volume-control {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .volume-control label {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .settings-content {
    width: 100%;
  }

  .settings-header h2 {
    font-size: var(--font-size-lg);
  }

  .settings-sections {
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-md);
  }

  .setting-item.horizontal {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .gradient-color-controls {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

/* Gradient Color Controls */
.gradient-color-controls {
  display: flex;
  gap: var(--spacing-md);
  margin-top: var(--spacing-sm);
}

.color-input-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex: 1;
}

.color-input-group label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

.color-input-group input[type="color"] {
  width: 100%;
  height: 40px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.color-input-group input[type="color"]:hover {
  border-color: var(--primary-color);
}
